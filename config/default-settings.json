{"app": {"name": "项老师超级总裁助理管理中心", "version": "1.0.0-2025-07-29-06-04-39", "lastStartTime": "", "autoStart": false}, "websocket": {"serverUrl": "wss://augment.diwangzhidao.com/ws", "reconnectDelay": 5000, "heartbeatInterval": 60000, "connectionTimeout": 30000}, "api": {"baseUrl": "https://www.diwangzhidao.com/MCP/zhuliguanlizhongxin/20250727/api.php", "smartStartUrl": "https://www.diwangzhidao.com/MCP/zhuliguanlizhongxin/20250727/qidong.php", "timeout": 30000}, "devices": {"001": {"enabled": true, "mode": "network", "interval": 10, "deviation": 1, "restRule": {"sendCount": 10, "restHours": 1}, "sentCount": 0, "isRunning": false, "lastSentTime": null, "messages": {"fixed": ["继续"], "random": ["继续", "下一步", "好的"], "local": ["继续", "下一步", "开始", "执行"], "network": {"template": "{publisher_name}发给项老师助理{assistant}的{task_id}号{task_type}任务：{content}，{other_requirements}"}}}, "002": {"enabled": true, "mode": "network", "interval": 10, "deviation": 1, "restRule": {"sendCount": 10, "restHours": 1}, "sentCount": 0, "isRunning": false, "lastSentTime": null, "messages": {"fixed": ["继续"], "random": ["继续", "下一步", "好的"], "local": ["继续", "下一步", "开始", "执行"], "network": {"template": "{publisher_name}发给项老师助理{assistant}的{task_id}号{task_type}任务：{content}，{other_requirements}"}}}, "003": {"enabled": true, "mode": "network", "interval": 10, "deviation": 1, "restRule": {"sendCount": 10, "restHours": 1}, "sentCount": 0, "isRunning": false, "lastSentTime": null, "messages": {"fixed": ["继续"], "random": ["继续", "下一步", "好的"], "local": ["继续", "下一步", "开始", "执行"], "network": {"template": "{publisher_name}发给项老师助理{assistant}的{task_id}号{task_type}任务：{content}，{other_requirements}"}}}, "004": {"enabled": true, "mode": "network", "interval": 10, "deviation": 1, "restRule": {"sendCount": 10, "restHours": 1}, "sentCount": 0, "isRunning": false, "lastSentTime": null, "messages": {"fixed": ["继续"], "random": ["继续", "下一步", "好的"], "local": ["继续", "下一步", "开始", "执行"], "network": {"template": "{publisher_name}发给项老师助理{assistant}的{task_id}号{task_type}任务：{content}，{other_requirements}"}}}, "005": {"enabled": true, "mode": "network", "interval": 10, "deviation": 1, "restRule": {"sendCount": 10, "restHours": 1}, "sentCount": 0, "isRunning": false, "lastSentTime": null, "messages": {"fixed": ["继续"], "random": ["继续", "下一步", "好的"], "local": ["继续", "下一步", "开始", "执行"], "network": {"template": "{publisher_name}发给项老师助理{assistant}的{task_id}号{task_type}任务：{content}，{other_requirements}"}}}, "006": {"enabled": true, "mode": "network", "interval": 10, "deviation": 1, "restRule": {"sendCount": 10, "restHours": 1}, "sentCount": 0, "isRunning": false, "lastSentTime": null, "messages": {"fixed": ["继续"], "random": ["继续", "下一步", "好的"], "local": ["继续", "下一步", "开始", "执行"], "network": {"template": "{publisher_name}发给项老师助理{assistant}的{task_id}号{task_type}任务：{content}，{other_requirements}"}}}}, "ui": {"theme": "dark", "particleCount": 50, "animationEnabled": true, "logLevel": "info"}}
# 项老师超级总裁助理指挥中心

## 项目简介

这是一个企业级多AI助理24小时不间断连续任务全自动指挥系统，用于统一管理和调度多个AI助理的工作任务。系统采用Electron桌面应用架构，实现了对6个AI助理（001-006）的智能任务分配、状态监控和自动化流转，确保AI助理团队24小时不间断高效运行。

## 核心功能

### 🚀 多设备管理
- 支持6个AI助理设备（001-006）的并发管理
- 实时WebSocket连接状态监控
- 自动重连机制，确保连接稳定性
- 设备在线状态可视化显示

### 💬 智能消息系统
- **固定模式**：发送预设的固定消息
- **随机模式**：从消息列表中随机选择发送
- **本地模式**：按顺序循环发送本地消息列表
- **网络模式**：从API获取任务并格式化发送

### ⏰ 智能时间控制
- 可配置发送间隔：10/15/20/30分钟
- 时间偏差设置：1/2/5/10分钟随机偏差
- 休息规则：发N次休N小时的灵活配置
- 智能间隔：接收完成消息后自动触发下一个任务

### 🎨 AI风格界面
- 深色科技主题，营造未来感
- 动态粒子背景系统
- 玻璃透明材质效果
- 呼吸灯和发光动画
- 响应式布局设计

## 技术架构

### 前端技术栈
- **Electron 28.3.3** - 跨平台桌面应用框架
- **HTML5 + CSS3** - 现代化界面技术
- **Tailwind CSS** - 实用优先的CSS框架
- **FontAwesome 6.x** - 图标库
- **Animate.css** - 动画效果库

### 后端通信
- **WebSocket** - 实时双向通信
- **RESTful API** - 任务数据获取和状态更新
- **JSON配置** - 本地设置持久化

### 核心服务
- **WebSocket服务器**: `wss://augment.diwangzhidao.com/ws`
- **API接口**: `https://www.diwangzhidao.com/MCP/zhuliguanlizhongxin/20250727/api.php`
- **智能启动**: `https://www.diwangzhidao.com/MCP/zhuliguanlizhongxin/20250727/qidong.php`

## 安装使用

### 系统要求
- Windows 10/11 (x64)
- macOS 10.14+ (Intel/Apple Silicon)
- Linux (x64)

### 快速开始
1. 下载可执行程序：`项老师超级总裁助理指挥中心-v1.0.13-最新版.exe`
2. 双击运行程序
3. 程序会自动连接WebSocket服务器
4. 配置各设备的消息模式和时间参数
5. 点击"开始"按钮启动24小时不间断自动化任务

### 配置说明
- 配置文件自动保存在程序同目录的`config/settings.json`
- 支持导入/导出配置
- 可随时恢复默认设置

## 功能详解

### 设备状态指示
- 🟢 **绿色**：设备在线，心跳正常
- 🟠 **橙色**：连接中，正在建立连接
- 🔴 **红色**：设备离线，连接断开

### 消息模式详解

#### 网络模式（推荐）
从服务器API获取实际任务，支持模板格式化：
```
{publisher_name}发给项老师助理{assistant}的{task_id}号{task_type}任务：{content}，{other_requirements}
```

#### 本地模式
按顺序循环发送预设消息列表，适合固定流程任务。

#### 随机模式
从消息列表中随机选择，增加任务多样性。

#### 固定模式
始终发送相同消息，适合简单重复任务。

### 批量操作
- **全部重连**：重新建立所有WebSocket连接
- **全部开始**：启动所有已启用的设备
- **全部停止**：停止所有设备的自动化任务
- **发送全部**：立即向所有设备发送一次消息

## 开发信息

### 项目结构
```
项老师超级总裁助理指挥中心/
├── main.js                 # Electron主进程
├── index.html              # 主界面
├── renderer.js             # 渲染进程逻辑
├── package.json            # 项目配置
├── assets/                 # 资源文件
│   └── icon.png           # 应用图标
├── config/                 # 配置文件
│   └── default-settings.json
└── dist/                   # 构建输出
    └── *.exe              # 可执行程序
```

### 构建命令
```bash
npm install                 # 安装依赖
npm start                   # 开发模式运行
npm run build-win          # 构建Windows版本
npm run build-mac          # 构建macOS版本
npm run build-linux        # 构建Linux版本
```

## 版本信息

- **版本号**: v1.0.13-2025-07-29-21-35-14
- **构建时间**: 2025年7月29日 21:35:14
- **开发者**: 项老师AI工作室
- **官网**: https://www.diwangzhidao.com

## 技术支持

如有问题或建议，请联系：
- **开发团队**: 项老师AI工作室
- **技术支持**: <EMAIL>
- **官方网站**: https://www.diwangzhidao.com

## 许可证

MIT License - 详见 LICENSE 文件

---

© 2025 项老师AI工作室 | 传统企业AI自动化转型专家

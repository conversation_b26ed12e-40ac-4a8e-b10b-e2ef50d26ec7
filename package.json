{"name": "xian<PERSON><PERSON><PERSON>-super-ceo-assistant-command-center", "version": "1.0.13-2025-07-29-21-35-14", "description": "项老师超级总裁助理指挥中心 - 多AI助理24小时不间断连续任务全自动指挥系统", "main": "main.js", "scripts": {"start": "electron .", "build-win": "electron-builder --win --x64", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux", "dist": "electron-builder"}, "keywords": ["项老师", "AI助理", "任务管理", "WebSocket", "桌面应用"], "author": "项老师AI工作室", "license": "MIT", "dependencies": {"ws": "^8.14.2", "basic-ftp": "^5.0.5", "node-fetch": "^3.3.2"}, "devDependencies": {"electron": "^28.3.3", "electron-builder": "^24.13.3"}, "build": {"appId": "com.xianglao.super-ceo-assistant-command-center", "productName": "项老师超级总裁助理指挥中心-v1.0.13-2025-07-29-21-35-14", "directories": {"output": "release-v1.0.13"}, "files": ["main.js", "index.html", "renderer.js", "assets/**/*", "config/**/*", "node_modules/**/*"], "win": {"target": [{"target": "portable", "arch": ["x64"]}], "icon": "assets/icon.png", "requestedExecutionLevel": "asInvoker"}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}], "icon": "assets/icon.png", "category": "public.app-category.productivity"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}], "icon": "assets/icon.png", "category": "Office"}, "portable": {"artifactName": "${productName}-${version}-${arch}.${ext}"}}, "homepage": "https://www.diwangzhidao.com", "repository": {"type": "git", "url": "https://github.com/xianglao/super-ceo-assistant-management-center"}}
const { app, BrowserWindow, Menu, ipcMain, dialog, Tray } = require('electron');
const path = require('path');
const fs = require('fs');

// 应用配置
const APP_CONFIG = {
  name: '项老师超级总裁助理指挥中心',
  version: '1.0.13-2025-07-29-21-35-14',
  description: '多AI助理24小时不间断连续任务全自动指挥系统',
  author: '项老师AI工作室',
  homepage: 'https://www.diwangzhidao.com'
};

let mainWindow;
let tray;

// 创建主窗口
function createWindow() {
  // 创建浏览器窗口
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 800,
    icon: path.join(__dirname, 'assets', 'icon.png'),
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true,
      webSecurity: false
    },
    titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'default',
    frame: process.platform !== 'win32', // Windows下隐藏默认标题栏
    show: false, // 先不显示，等加载完成后再显示
    backgroundColor: '#000000' // 深色背景
  });

  // 加载主页面
  mainWindow.loadFile('index.html');

  // 窗口准备好后显示
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
    
    // 开发环境下打开开发者工具
    if (process.env.NODE_ENV === 'development') {
      mainWindow.webContents.openDevTools();
    }
  });

  // 窗口关闭事件 - 最小化到托盘而不是退出
  mainWindow.on('close', (event) => {
    if (!app.isQuiting) {
      event.preventDefault();
      mainWindow.hide();

      // 首次最小化到托盘时显示提示
      if (!tray.isDestroyed()) {
        tray.displayBalloon({
          iconType: 'info',
          title: '项老师超级总裁助理指挥中心',
          content: '程序已最小化到系统托盘，点击托盘图标可重新打开'
        });
      }
    }
  });

  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // 设置菜单
  if (process.platform === 'darwin') {
    // macOS菜单
    const template = [
      {
        label: APP_CONFIG.name,
        submenu: [
          { role: 'about' },
          { type: 'separator' },
          { role: 'services' },
          { type: 'separator' },
          { role: 'hide' },
          { role: 'hideothers' },
          { role: 'unhide' },
          { type: 'separator' },
          { role: 'quit' }
        ]
      },
      {
        label: '编辑',
        submenu: [
          { role: 'undo' },
          { role: 'redo' },
          { type: 'separator' },
          { role: 'cut' },
          { role: 'copy' },
          { role: 'paste' },
          { role: 'selectall' }
        ]
      },
      {
        label: '视图',
        submenu: [
          { role: 'reload' },
          { role: 'forceReload' },
          { role: 'toggleDevTools' },
          { type: 'separator' },
          { role: 'resetZoom' },
          { role: 'zoomIn' },
          { role: 'zoomOut' },
          { type: 'separator' },
          { role: 'togglefullscreen' }
        ]
      },
      {
        label: '窗口',
        submenu: [
          { role: 'minimize' },
          { role: 'close' }
        ]
      }
    ];
    
    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
  } else {
    // Windows/Linux - 隐藏菜单栏
    Menu.setApplicationMenu(null);
  }
}

// 创建系统托盘
function createTray() {
  const iconPath = path.join(__dirname, 'assets', 'icon.png');
  tray = new Tray(iconPath);

  // 设置托盘提示文字
  tray.setToolTip('项老师超级总裁助理指挥中心');

  // 创建托盘右键菜单
  const contextMenu = Menu.buildFromTemplate([
    {
      label: '显示主窗口',
      click: () => {
        if (mainWindow) {
          mainWindow.show();
          mainWindow.focus();
        } else {
          createWindow();
        }
      }
    },
    {
      label: '关于',
      click: () => {
        dialog.showMessageBox(mainWindow || null, {
          type: 'info',
          title: '关于',
          message: APP_CONFIG.name,
          detail: `版本: ${APP_CONFIG.version}\n作者: ${APP_CONFIG.author}\n官网: ${APP_CONFIG.homepage}`,
          buttons: ['确定']
        });
      }
    },
    { type: 'separator' },
    {
      label: '退出程序',
      click: () => {
        app.isQuiting = true;
        app.quit();
      }
    }
  ]);

  tray.setContextMenu(contextMenu);

  // 双击托盘图标显示/隐藏窗口
  tray.on('double-click', () => {
    if (mainWindow) {
      if (mainWindow.isVisible()) {
        mainWindow.hide();
      } else {
        mainWindow.show();
        mainWindow.focus();
      }
    } else {
      createWindow();
    }
  });

  // 单击托盘图标显示窗口
  tray.on('click', () => {
    if (mainWindow) {
      mainWindow.show();
      mainWindow.focus();
    } else {
      createWindow();
    }
  });
}

// 应用准备就绪
app.whenReady().then(() => {
  createWindow();
  createTray();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

// 所有窗口关闭时不退出应用，保持托盘运行
app.on('window-all-closed', () => {
  // 在macOS上，除非用户明确退出，否则保持应用运行
  // 在Windows/Linux上，也保持托盘运行
  if (process.platform === 'darwin' && !app.isQuiting) {
    // macOS上隐藏dock图标但保持应用运行
    app.dock?.hide();
  }
  // 不调用app.quit()，让应用继续在托盘中运行
});

// IPC通信处理
ipcMain.handle('get-app-info', () => {
  return APP_CONFIG;
});

ipcMain.handle('show-error-dialog', async (event, title, content) => {
  const result = await dialog.showMessageBox(mainWindow, {
    type: 'error',
    title: title,
    message: content,
    buttons: ['确定']
  });
  return result;
});

ipcMain.handle('show-info-dialog', async (event, title, content) => {
  const result = await dialog.showMessageBox(mainWindow, {
    type: 'info',
    title: title,
    message: content,
    buttons: ['确定']
  });
  return result;
});

// 窗口控制IPC处理
ipcMain.on('window-minimize', () => {
  if (mainWindow) {
    // 最小化到托盘而不是任务栏
    mainWindow.hide();

    // 显示托盘提示
    if (tray && !tray.isDestroyed()) {
      tray.displayBalloon({
        iconType: 'info',
        title: '项老师超级总裁助理指挥中心',
        content: '程序已最小化到系统托盘'
      });
    }
  }
});

ipcMain.on('window-maximize', () => {
  if (mainWindow) {
    if (mainWindow.isMaximized()) {
      mainWindow.unmaximize();
    } else {
      mainWindow.maximize();
    }
  }
});

ipcMain.on('window-close', () => {
  if (mainWindow) {
    // 关闭按钮也是最小化到托盘
    mainWindow.hide();

    // 显示托盘提示
    if (tray && !tray.isDestroyed()) {
      tray.displayBalloon({
        iconType: 'info',
        title: '项老师超级总裁助理指挥中心',
        content: '程序已最小化到系统托盘，右键托盘图标可选择退出'
      });
    }
  }
});

// 配置文件操作
ipcMain.handle('save-config', async (event, config) => {
  try {
    const configPath = path.join(__dirname, 'config', 'settings.json');
    fs.writeFileSync(configPath, JSON.stringify(config, null, 2), 'utf8');
    return { success: true };
  } catch (error) {
    console.error('保存配置失败:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('load-config', async () => {
  try {
    const configPath = path.join(__dirname, 'config', 'settings.json');
    if (fs.existsSync(configPath)) {
      const config = fs.readFileSync(configPath, 'utf8');
      return { success: true, data: JSON.parse(config) };
    } else {
      return { success: false, error: '配置文件不存在' };
    }
  } catch (error) {
    console.error('加载配置失败:', error);
    return { success: false, error: error.message };
  }
});

// 应用退出前清理
app.on('before-quit', () => {
  console.log('应用即将退出，执行清理操作...');
  app.isQuiting = true;

  // 清理托盘
  if (tray && !tray.isDestroyed()) {
    tray.destroy();
  }
});

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason);
});

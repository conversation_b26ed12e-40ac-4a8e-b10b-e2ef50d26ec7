# 项老师超级总裁助理指挥中心 - 连接服务器方式和消息格式完整说明

## 🔗 WebSocket连接配置

**服务器地址**: wss://augment.diwangzhidao.com/ws
**连接协议**: WebSocket Secure (WSS)
**连接超时**: 30秒
**重连间隔**: 5秒
**心跳超时**: 60秒
**连接机制**: 每个设备(001-006)建立独立WebSocket连接，对应GROUP_001到GROUP_006

## 📱 设备注册消息格式

### 手机端注册消息
```json
{
  "type": "register",
  "deviceId": "项老师苹果手机001",
  "deviceType": "mobile",
  "pairId": "GROUP_001",
  "userId": "项老师001",
  "groupDisplayName": "零零一组",
  "timestamp": 1722222222222
}
```

### 电脑端注册消息（由电脑端发送）
```json
{
  "type": "register",
  "deviceId": "DESKTOP-ABC123拯救程序员的电脑001",
  "deviceType": "computer",
  "pairId": "GROUP_001",
  "userId": "项老师001",
  "groupDisplayName": "零零一组",
  "timestamp": 1722222222222
}
```

## 💓 心跳检测消息格式

```json
{
  "type": "heartbeat",
  "deviceId": "DESKTOP-ABC123拯救程序员的电脑001",
  "timestamp": "2025-07-29 06:45:00",
  "status": "online"
}
```

**心跳处理规则**:
- 消息必须包含"电脑"关键字
- 使用正则表达式提取电脑编号
- 手机001只接收电脑001的心跳
- 60秒内未收到心跳判定为离线

## 📤 用户消息发送格式

```json
{
  "type": "user_message",
  "data": {
    "fromDevice": "项老师苹果手机001",
    "message": "项老师发给项老师助理001的123号编程任务：创建一个网页，要求响应式设计",
    "fromGroup": "GROUP_001",
    "groupDisplayName": "零零一组",
    "timestamp": 1722222222222
  }
}
```

## 📥 服务器响应消息格式

### 欢迎消息
```json
{
  "type": "welcome",
  "message": "设备注册成功",
  "deviceId": "项老师苹果手机001",
  "groupId": "GROUP_001"
}
```

### AI回复消息
```json
{
  "type": "ai_reply",
  "data": {
    "message": "收到任务，正在执行...",
    "fromDevice": "电脑001",
    "timestamp": "2025-07-29 06:45:00"
  }
}
```

### AI状态更新
```json
{
  "type": "ai_status",
  "data": {
    "status": "working",
    "progress": "50%",
    "currentTask": "创建网页"
  }
}
```

## 🔄 消息模板变量（网络模式）

**默认模板**: {publisher_name}发给项老师助理{assistant}的{task_id}号{task_type}任务：{content}，{other_requirements}

**支持变量**:
- {publisher_name} - 发布者姓名
- {assistant} - 助理编号（001-006）
- {task_id} - 任务ID
- {task_type} - 任务类型
- {content} - 任务内容
- {other_requirements} - 其他要求

## 🔄 完整通信流程

1. **连接建立**: 手机端连接WebSocket服务器
2. **设备注册**: 发送注册消息到指定GROUP
3. **等待配对**: 等待电脑端连接同一GROUP
4. **心跳监控**: 持续监听电脑端心跳消息
5. **任务发送**: 发送格式化的任务指令
6. **状态接收**: 接收AI执行状态和结果反馈

## 🛡️ 安全机制

**连接安全**: WSS加密连接、设备身份验证、GROUP隔离机制、消息类型验证
**错误处理**: 连接异常自动重连、消息解析错误处理、心跳超时检测、详细错误日志记录

## 📊 设备编号规则

**手机端**: 项老师苹果手机001-006
**电脑端**: 包含"电脑"关键字和编号001-006的设备名
**GROUP**: GROUP_001到GROUP_006，实现1对1配对
**编号提取**: 支持多种正则表达式模式提取电脑编号

## ⚙️ 系统配置参数

**API接口**: https://www.diwangzhidao.com/MCP/zhuliguanlizhongxin/20250727/api.php
**智能启动**: https://www.diwangzhidao.com/MCP/zhuliguanlizhongxin/20250727/qidong.php
**重连机制**: 最多重试10次，每次间隔递增
**状态检查**: 每10秒检查一次设备在线状态
**消息限制**: 最多保存1000条实时消息和1000条执行日志

---
© 2025 项老师AI工作室 | 项老师超级总裁助理指挥中心技术文档

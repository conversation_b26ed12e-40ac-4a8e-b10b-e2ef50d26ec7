// 项老师超级总裁助理管理中心 - 渲染进程
// 版本: 1.0.0-2025-07-29-06-04-39

const { ipc<PERSON><PERSON><PERSON> } = require('electron');
const WebSocket = require('ws');
const fs = require('fs');
const path = require('path');

// 全局配置
const CONFIG = {
    WS_SERVER: 'wss://augment.diwangzhidao.com/ws',
    API_BASE_URL: 'https://www.diwangzhidao.com/MCP/zhuliguanlizhongxin/20250727/api.php',
    SMART_START_URL: 'https://www.diwangzhidao.com/MCP/zhuliguanlizhongxin/20250727/qidong.php',
    RECONNECT_DELAY: 5000,
    HEARTBEAT_TIMEOUT: 60000,
    STATUS_CHECK_INTERVAL: 10000
};

// 全局状态管理
class AppState {
    constructor() {
        this.devices = new Map();
        this.webSockets = new Map();
        this.timers = new Map();
        this.settings = null;
        this.isInitialized = false;
        
        // 初始化设备状态
        for (let i = 1; i <= 6; i++) {
            const deviceId = i.toString().padStart(3, '0');
            this.devices.set(deviceId, {
                id: deviceId,
                enabled: true,
                isOnline: false,
                isRunning: false,
                mode: 'network',
                interval: 10,
                deviation: 1,
                restRule: { sendCount: 10, restHours: 1 },
                sentCount: 0,
                lastHeartbeat: null,
                heartbeatCount: 0,
                lastSentTime: null,
                nextSendTime: null,
                currentTask: null
            });
        }
    }

    getDevice(deviceId) {
        return this.devices.get(deviceId);
    }

    updateDevice(deviceId, updates) {
        const device = this.devices.get(deviceId);
        if (device) {
            Object.assign(device, updates);
            this.updateDeviceUI(deviceId);
        }
    }

    updateDeviceUI(deviceId) {
        const device = this.getDevice(deviceId);
        if (!device) return;

        // 更新状态指示器
        const statusEl = document.getElementById(`device${deviceId}-status`);
        if (statusEl) {
            statusEl.className = `w-8 h-8 rounded-full breathing flex items-center justify-center text-white font-bold text-sm ${
                device.isOnline ? 'status-online glow-green' : 'status-offline glow-red'
            }`;
        }

        // 更新已发次数
        const sentEl = document.getElementById(`device${deviceId}-sent`);
        if (sentEl) {
            sentEl.textContent = `已发${device.sentCount}次`;
        }

        // 更新运行状态按钮
        const toggleEl = document.getElementById(`device${deviceId}-toggle`);
        if (toggleEl) {
            toggleEl.textContent = device.isRunning ? '暂停' : '开始';
            toggleEl.className = `px-4 py-2 rounded-lg text-sm transition-colors ${
                device.isRunning ? 'bg-yellow-600 hover:bg-yellow-700' : 'bg-green-600 hover:bg-green-700'
            }`;
        }
    }
}

// WebSocket连接管理器
class WebSocketManager {
    constructor(appState) {
        this.appState = appState;
        this.reconnectAttempts = new Map();
        this.maxReconnectAttempts = 10;
    }

    // 连接到指定设备的WebSocket
    connectDevice(deviceId) {
        const groupId = `GROUP_${deviceId}`;
        const deviceName = `项老师苹果手机${deviceId}`;

        console.log(`🔗 开始连接设备 ${deviceId} 到 ${groupId}`);
        logManager.addExecutionLog('info', 'websocket', `开始连接设备 ${deviceId}`, { groupId, deviceName });

        try {
            const ws = new WebSocket(CONFIG.WS_SERVER);

            ws.on('open', () => {
                console.log(`✅ 设备 ${deviceId} WebSocket连接已建立`);
                logManager.addExecutionLog('success', 'websocket', `设备 ${deviceId} 连接成功`);
                logManager.addRealTimeMessage('connect', deviceId, `WebSocket连接已建立`);

                this.reconnectAttempts.set(deviceId, 0);

                // 注册设备
                this.registerDevice(ws, deviceId, groupId, deviceName);

                // 存储连接
                this.appState.webSockets.set(deviceId, ws);
            });

            ws.on('message', (data) => {
                this.handleMessage(deviceId, data);
            });

            ws.on('close', (code, reason) => {
                console.log(`❌ 设备 ${deviceId} WebSocket连接已关闭: ${code} ${reason}`);
                logManager.addExecutionLog('warning', 'websocket', `设备 ${deviceId} 连接关闭`, { code, reason });
                logManager.addRealTimeMessage('disconnect', deviceId, `连接已关闭 (${code})`);

                this.appState.webSockets.delete(deviceId);
                this.appState.updateDevice(deviceId, { isOnline: false });

                // 自动重连
                this.scheduleReconnect(deviceId);
            });

            ws.on('error', (error) => {
                console.error(`🚨 设备 ${deviceId} WebSocket错误:`, error);
                logManager.addExecutionLog('error', 'websocket', `设备 ${deviceId} 连接错误`, error);
                logManager.addRealTimeMessage('error', deviceId, `连接错误: ${error.message}`);

                this.appState.updateDevice(deviceId, { isOnline: false });
            });

        } catch (error) {
            console.error(`🚨 设备 ${deviceId} 连接失败:`, error);
            logManager.addExecutionLog('error', 'websocket', `设备 ${deviceId} 连接失败`, error);
            logManager.addRealTimeMessage('error', deviceId, `连接失败: ${error.message}`);
            this.scheduleReconnect(deviceId);
        }
    }

    // 注册设备到服务器
    registerDevice(ws, deviceId, groupId, deviceName) {
        const registerMessage = {
            type: 'register',
            deviceId: deviceName,
            deviceType: 'mobile',
            pairId: groupId,
            userId: `项老师${deviceId}`,
            groupDisplayName: `零零${deviceId}组`,
            timestamp: Date.now()
        };

        ws.send(JSON.stringify(registerMessage));
        console.log(`📱 设备 ${deviceId} 注册消息已发送:`, registerMessage);
        logManager.addExecutionLog('info', 'websocket', `设备 ${deviceId} 注册消息已发送`);
        logManager.addRealTimeMessage('send', deviceId, '设备注册消息', registerMessage);
    }

    // 处理接收到的消息
    handleMessage(deviceId, data) {
        try {
            const message = JSON.parse(data.toString());
            console.log(`📨 设备 ${deviceId} 收到消息:`, message);

            logManager.addRealTimeMessage('receive', deviceId, `收到${message.type}消息`, message);

            switch (message.type) {
                case 'welcome':
                    console.log(`🎉 设备 ${deviceId} 注册成功`);
                    logManager.addExecutionLog('success', 'websocket', `设备 ${deviceId} 注册成功`);
                    logManager.addRealTimeMessage('status', deviceId, '设备注册成功');
                    break;

                case 'heartbeat':
                case 'client_heartbeat':
                    this.handleHeartbeat(deviceId, message);
                    break;

                case 'ai_reply':
                    console.log(`🤖 设备 ${deviceId} 收到AI回复:`, message.data?.message);
                    logManager.addExecutionLog('info', 'websocket', `设备 ${deviceId} 收到AI回复`);
                    logManager.addRealTimeMessage('receive', deviceId, 'AI回复消息', message.data);
                    break;

                case 'ai_status':
                    console.log(`📊 设备 ${deviceId} AI状态更新:`, message.data?.status);
                    logManager.addExecutionLog('info', 'websocket', `设备 ${deviceId} AI状态更新: ${message.data?.status}`);
                    logManager.addRealTimeMessage('status', deviceId, `AI状态: ${message.data?.status}`);
                    break;

                default:
                    console.log(`❓ 设备 ${deviceId} 未知消息类型:`, message.type);
                    logManager.addExecutionLog('warning', 'websocket', `设备 ${deviceId} 未知消息类型: ${message.type}`);
            }
        } catch (error) {
            console.error(`🚨 设备 ${deviceId} 消息解析错误:`, error);
            logManager.addExecutionLog('error', 'websocket', `设备 ${deviceId} 消息解析错误`, error);
            logManager.addRealTimeMessage('error', deviceId, `消息解析错误: ${error.message}`);
        }
    }

    // 处理心跳消息
    handleHeartbeat(deviceId, message) {
        const deviceIdFromMessage = message.deviceId;
        
        // 验证是否为电脑心跳
        if (!deviceIdFromMessage || !deviceIdFromMessage.includes('电脑')) {
            return;
        }

        // 提取电脑编号
        const computerNumber = this.extractComputerNumber(deviceIdFromMessage);
        if (computerNumber === deviceId) {
            console.log(`💓 设备 ${deviceId} 收到匹配的电脑心跳: ${deviceIdFromMessage}`);
            logManager.addRealTimeMessage('heartbeat', deviceId, `收到电脑心跳: ${deviceIdFromMessage}`);

            const device = this.appState.getDevice(deviceId);
            if (device) {
                this.appState.updateDevice(deviceId, {
                    isOnline: true,
                    lastHeartbeat: new Date(),
                    heartbeatCount: device.heartbeatCount + 1
                });

                logManager.addExecutionLog('info', 'websocket', `设备 ${deviceId} 心跳更新`, {
                    heartbeatCount: device.heartbeatCount + 1,
                    computerDevice: deviceIdFromMessage
                });
            }
        }
    }

    // 从设备ID中提取电脑编号
    extractComputerNumber(deviceId) {
        const patterns = [
            /电脑(\d{3})$/,
            /电脑(\d{3})[^\d]*$/,
            /电脑.*?(\d{3})/,
            /电脑(\d{1,3})/
        ];

        for (const pattern of patterns) {
            const match = deviceId.match(pattern);
            if (match) {
                return match[1].padStart(3, '0');
            }
        }
        return null;
    }

    // 安排重连
    scheduleReconnect(deviceId) {
        const attempts = this.reconnectAttempts.get(deviceId) || 0;
        
        if (attempts < this.maxReconnectAttempts) {
            const delay = CONFIG.RECONNECT_DELAY * (attempts + 1);
            console.log(`🔄 设备 ${deviceId} 将在 ${delay}ms 后重连 (第${attempts + 1}次尝试)`);
            
            setTimeout(() => {
                this.reconnectAttempts.set(deviceId, attempts + 1);
                this.connectDevice(deviceId);
            }, delay);
        } else {
            console.error(`❌ 设备 ${deviceId} 重连失败，已达到最大重试次数`);
        }
    }

    // 发送用户消息
    sendUserMessage(deviceId, message) {
        const ws = this.appState.webSockets.get(deviceId);
        if (!ws || ws.readyState !== WebSocket.OPEN) {
            console.error(`❌ 设备 ${deviceId} WebSocket未连接，无法发送消息`);
            return false;
        }

        const userMessage = {
            type: 'user_message',
            data: {
                fromDevice: `项老师苹果手机${deviceId}`,
                message: message,
                fromGroup: `GROUP_${deviceId}`,
                groupDisplayName: `零零${deviceId}组`,
                timestamp: Date.now()
            }
        };

        try {
            ws.send(JSON.stringify(userMessage));
            console.log(`📤 设备 ${deviceId} 消息已发送:`, message);
            logManager.addExecutionLog('success', 'websocket', `设备 ${deviceId} 消息发送成功`);
            logManager.addRealTimeMessage('send', deviceId, `发送消息: ${message}`, userMessage);
            return true;
        } catch (error) {
            console.error(`🚨 设备 ${deviceId} 发送消息失败:`, error);
            logManager.addExecutionLog('error', 'websocket', `设备 ${deviceId} 消息发送失败`, error);
            logManager.addRealTimeMessage('error', deviceId, `发送失败: ${error.message}`);
            return false;
        }
    }

    // 连接所有设备
    connectAll() {
        console.log('🚀 开始连接所有设备...');
        
        for (let i = 1; i <= 6; i++) {
            const deviceId = i.toString().padStart(3, '0');
            
            // 延迟连接，避免同时建立太多连接
            setTimeout(() => {
                this.connectDevice(deviceId);
            }, i * 1000);
        }
    }

    // 断开所有连接
    disconnectAll() {
        console.log('🔌 断开所有WebSocket连接...');
        
        this.appState.webSockets.forEach((ws, deviceId) => {
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.close();
            }
        });
        
        this.appState.webSockets.clear();
    }
}

// 全局实例
const appState = new AppState();
const wsManager = new WebSocketManager(appState);

// 日志管理系统
class LogManager {
    constructor() {
        this.realTimeMessages = [];
        this.executionLogs = [];
        this.maxMessages = 1000; // 最大消息数量
        this.maxLogs = 1000; // 最大日志数量
    }

    // 添加实时消息
    addRealTimeMessage(type, deviceId, message, data = null) {
        const timestamp = new Date().toLocaleString();
        const logEntry = {
            timestamp,
            type, // 'send', 'receive', 'connect', 'disconnect', 'error'
            deviceId,
            message,
            data
        };

        this.realTimeMessages.unshift(logEntry);

        // 限制消息数量
        if (this.realTimeMessages.length > this.maxMessages) {
            this.realTimeMessages = this.realTimeMessages.slice(0, this.maxMessages);
        }

        // 更新实时消息窗口（如果打开）
        this.updateRealTimeDisplay();
    }

    // 添加执行日志
    addExecutionLog(level, category, message, details = null) {
        const timestamp = new Date().toLocaleString();
        const logEntry = {
            timestamp,
            level, // 'info', 'success', 'warning', 'error'
            category, // 'system', 'websocket', 'api', 'ui', 'config'
            message,
            details
        };

        this.executionLogs.unshift(logEntry);

        // 限制日志数量
        if (this.executionLogs.length > this.maxLogs) {
            this.executionLogs = this.executionLogs.slice(0, this.maxLogs);
        }

        // 更新执行日志窗口（如果打开）
        this.updateExecutionLogDisplay();

        // 同时输出到控制台
        const consoleMessage = `[${timestamp}] [${level.toUpperCase()}] [${category}] ${message}`;
        switch (level) {
            case 'error':
                console.error(consoleMessage, details);
                break;
            case 'warning':
                console.warn(consoleMessage, details);
                break;
            case 'success':
                console.log(`%c${consoleMessage}`, 'color: #22c55e', details);
                break;
            default:
                console.log(consoleMessage, details);
        }
    }

    // 更新实时消息显示
    updateRealTimeDisplay() {
        const container = document.getElementById('realtime-messages');
        if (!container) return;

        const html = this.realTimeMessages.map(entry => {
            const typeColors = {
                'send': 'text-blue-400',
                'receive': 'text-green-400',
                'connect': 'text-cyan-400',
                'disconnect': 'text-red-400',
                'error': 'text-red-500',
                'heartbeat': 'text-purple-400',
                'status': 'text-yellow-400'
            };

            const typeIcons = {
                'send': '📤',
                'receive': '📥',
                'connect': '🔗',
                'disconnect': '❌',
                'error': '🚨',
                'heartbeat': '💓',
                'status': '📊'
            };

            const color = typeColors[entry.type] || 'text-gray-400';
            const icon = typeIcons[entry.type] || '📝';

            return `
                <div class="mb-2 font-mono text-sm">
                    <span class="text-gray-500">[${entry.timestamp}]</span>
                    <span class="${color}">${icon} [设备${entry.deviceId || 'SYS'}]</span>
                    <span class="text-white">${entry.message}</span>
                    ${entry.data ? `<div class="ml-4 text-gray-400 text-xs">${JSON.stringify(entry.data, null, 2)}</div>` : ''}
                </div>
            `;
        }).join('');

        container.innerHTML = html || '<div class="text-gray-400">暂无消息</div>';

        // 自动滚动到顶部（最新消息）
        container.scrollTop = 0;
    }

    // 更新执行日志显示
    updateExecutionLogDisplay() {
        const container = document.getElementById('execution-log');
        if (!container) return;

        const html = this.executionLogs.map(entry => {
            const levelColors = {
                'info': 'text-blue-400',
                'success': 'text-green-400',
                'warning': 'text-yellow-400',
                'error': 'text-red-400'
            };

            const levelIcons = {
                'info': 'ℹ️',
                'success': '✅',
                'warning': '⚠️',
                'error': '❌'
            };

            const color = levelColors[entry.level] || 'text-gray-400';
            const icon = levelIcons[entry.level] || '📝';

            return `
                <div class="mb-2 text-sm">
                    <span class="text-gray-500">[${entry.timestamp}]</span>
                    <span class="${color}">${icon} [${entry.category.toUpperCase()}]</span>
                    <span class="text-white">${entry.message}</span>
                    ${entry.details ? `<div class="ml-4 text-gray-400 text-xs">${JSON.stringify(entry.details, null, 2)}</div>` : ''}
                </div>
            `;
        }).join('');

        container.innerHTML = html || '<div class="text-gray-400">暂无日志</div>';

        // 自动滚动到顶部（最新日志）
        container.scrollTop = 0;
    }

    // 清空实时消息
    clearRealTimeMessages() {
        this.realTimeMessages = [];
        this.updateRealTimeDisplay();
    }

    // 清空执行日志
    clearExecutionLogs() {
        this.executionLogs = [];
        this.updateExecutionLogDisplay();
    }

    // 导出日志
    exportLogs() {
        const data = {
            exportTime: new Date().toISOString(),
            realTimeMessages: this.realTimeMessages,
            executionLogs: this.executionLogs
        };

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `logs-export-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }
}

// 全局日志管理器
const logManager = new LogManager();

// 应用初始化
async function initializeApp() {
    console.log('🚀 初始化项老师超级总裁助理指挥中心...');
    logManager.addExecutionLog('info', 'system', '应用开始初始化');

    try {
        // 加载配置
        logManager.addExecutionLog('info', 'config', '开始加载配置文件');
        await loadSettings();

        // 初始化粒子背景
        logManager.addExecutionLog('info', 'ui', '初始化粒子背景系统');
        initializeParticles();

        // 检测操作系统并显示窗口控制按钮
        logManager.addExecutionLog('info', 'system', '检测操作系统类型');
        detectOS();

        // 连接所有WebSocket
        logManager.addExecutionLog('info', 'websocket', '开始连接所有WebSocket');
        wsManager.connectAll();

        // 启动状态检查定时器
        logManager.addExecutionLog('info', 'system', '启动设备状态检查定时器');
        startStatusChecker();

        // 启动倒计时更新
        logManager.addExecutionLog('info', 'system', '启动倒计时更新器');
        startCountdownUpdater();

        appState.isInitialized = true;
        console.log('✅ 应用初始化完成');
        logManager.addExecutionLog('success', 'system', '应用初始化完成');

    } catch (error) {
        console.error('🚨 应用初始化失败:', error);
        logManager.addExecutionLog('error', 'system', '应用初始化失败', error);
    }
}

// 消息发送系统
class MessageSender {
    constructor(appState, wsManager) {
        this.appState = appState;
        this.wsManager = wsManager;
    }

    // 获取设备的下一条消息
    async getNextMessage(deviceId) {
        const device = this.appState.getDevice(deviceId);
        if (!device) return null;

        const settings = this.appState.settings?.devices?.[deviceId];
        if (!settings) return '继续';

        switch (device.mode) {
            case 'fixed':
                return settings.messages?.fixed?.[0] || '继续';

            case 'random':
                const randomMessages = settings.messages?.random || ['继续'];
                return randomMessages[Math.floor(Math.random() * randomMessages.length)];

            case 'local':
                const localMessages = settings.messages?.local || ['继续'];
                const index = device.sentCount % localMessages.length;
                return localMessages[index];

            case 'network':
                return await this.getNetworkMessage(deviceId);

            default:
                return '继续';
        }
    }

    // 获取网络任务消息
    async getNetworkMessage(deviceId) {
        try {
            const response = await fetch(`${CONFIG.API_BASE_URL}?action=get_next_task&assistant=${deviceId}`);
            const result = await response.json();

            if (result.success && result.task) {
                const task = result.task;
                const template = this.appState.settings?.devices?.[deviceId]?.messages?.network?.template ||
                    '{publisher_name}发给项老师助理{assistant}的{task_id}号{task_type}任务：{content}，{other_requirements}';

                // 更新任务状态为进行中
                await this.updateTaskStatus(deviceId, task.task_id, '🔄 进行中');

                // 格式化消息
                return this.formatMessage(template, task);
            } else {
                console.log(`📭 设备 ${deviceId} 暂无待处理任务`);
                return null;
            }
        } catch (error) {
            console.error(`🚨 设备 ${deviceId} 获取网络任务失败:`, error);
            return null;
        }
    }

    // 格式化消息模板
    formatMessage(template, task) {
        return template
            .replace(/{publisher_name}/g, task.publisher_name || '项老师')
            .replace(/{assistant}/g, task.assistant)
            .replace(/{task_id}/g, task.task_id)
            .replace(/{task_type}/g, task.task_type || '任务')
            .replace(/{content}/g, task.content)
            .replace(/{other_requirements}/g, task.other_requirements || '请按照标准格式完成');
    }

    // 更新任务状态
    async updateTaskStatus(deviceId, taskId, status) {
        try {
            const formData = new FormData();
            formData.append('action', 'update_status');
            formData.append('assistant', deviceId);
            formData.append('task_id', taskId);
            formData.append('status', status);

            const response = await fetch(CONFIG.API_BASE_URL, {
                method: 'POST',
                body: formData
            });

            const result = await response.json();
            console.log(`📊 设备 ${deviceId} 任务 ${taskId} 状态更新为: ${status}`);
            return result.success;
        } catch (error) {
            console.error(`🚨 设备 ${deviceId} 更新任务状态失败:`, error);
            return false;
        }
    }

    // 发送消息
    async sendMessage(deviceId) {
        const device = this.appState.getDevice(deviceId);
        if (!device || !device.enabled) {
            console.log(`⏸️ 设备 ${deviceId} 未启用，跳过发送`);
            return false;
        }

        const message = await this.getNextMessage(deviceId);
        if (!message) {
            console.log(`📭 设备 ${deviceId} 无消息可发送`);
            return false;
        }

        const success = this.wsManager.sendUserMessage(deviceId, message);
        if (success) {
            this.appState.updateDevice(deviceId, {
                sentCount: device.sentCount + 1,
                lastSentTime: new Date()
            });

            // 计算下次发送时间
            this.scheduleNextSend(deviceId);
        }

        return success;
    }

    // 安排下次发送
    scheduleNextSend(deviceId) {
        const device = this.appState.getDevice(deviceId);
        if (!device || !device.isRunning) return;

        // 计算间隔时间（分钟转毫秒）
        const baseInterval = device.interval * 60 * 1000;
        const deviation = device.deviation * 60 * 1000;
        const randomDeviation = (Math.random() - 0.5) * 2 * deviation;
        const actualInterval = baseInterval + randomDeviation;

        const nextSendTime = new Date(Date.now() + actualInterval);
        this.appState.updateDevice(deviceId, { nextSendTime });

        // 设置定时器
        const timerId = setTimeout(() => {
            if (device.isRunning) {
                this.sendMessage(deviceId);
            }
        }, actualInterval);

        // 存储定时器ID
        const existingTimer = this.appState.timers.get(`send_${deviceId}`);
        if (existingTimer) {
            clearTimeout(existingTimer);
        }
        this.appState.timers.set(`send_${deviceId}`, timerId);
    }
}

// 全局消息发送器实例
const messageSender = new MessageSender(appState, wsManager);

// UI工具函数
function initializeParticles() {
    const container = document.getElementById('particles');
    if (!container) return;

    // 创建50个粒子
    for (let i = 0; i < 50; i++) {
        const particle = document.createElement('div');
        particle.className = 'particle';

        // 随机位置和透明度
        particle.style.left = Math.random() * 100 + '%';
        particle.style.opacity = Math.random() * 0.7 + 0.1;
        particle.style.animationDelay = Math.random() * 20 + 's';
        particle.style.animationDuration = (Math.random() * 10 + 15) + 's';

        container.appendChild(particle);
    }
}

function detectOS() {
    const isWindows = navigator.platform.indexOf('Win') > -1;
    if (isWindows) {
        const windowControls = document.getElementById('windowControls');
        if (windowControls) {
            windowControls.style.display = 'flex';
        }
    }
}

// 状态检查器
function startStatusChecker() {
    setInterval(() => {
        const now = new Date();

        appState.devices.forEach((device, deviceId) => {
            if (device.lastHeartbeat) {
                const timeSinceLastHeartbeat = now - device.lastHeartbeat;
                const isOnline = timeSinceLastHeartbeat <= CONFIG.HEARTBEAT_TIMEOUT;

                if (device.isOnline !== isOnline) {
                    appState.updateDevice(deviceId, { isOnline });
                }
            }
        });
    }, CONFIG.STATUS_CHECK_INTERVAL);
}

// 倒计时更新器
function startCountdownUpdater() {
    setInterval(() => {
        appState.devices.forEach((device, deviceId) => {
            if (device.nextSendTime && device.isRunning) {
                const now = new Date();
                const timeLeft = device.nextSendTime - now;

                if (timeLeft > 0) {
                    const minutes = Math.floor(timeLeft / 60000);
                    const seconds = Math.floor((timeLeft % 60000) / 1000);
                    const countdown = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

                    const countdownEl = document.getElementById(`device${deviceId}-countdown`);
                    if (countdownEl) {
                        countdownEl.textContent = countdown;
                    }
                } else {
                    const countdownEl = document.getElementById(`device${deviceId}-countdown`);
                    if (countdownEl) {
                        countdownEl.textContent = '00:00';
                    }
                }
            }
        });
    }, 1000);
}

// 配置管理
async function loadSettings() {
    try {
        const result = await ipcRenderer.invoke('load-config');
        if (result.success) {
            appState.settings = result.data;
            console.log('✅ 配置加载成功');
        } else {
            // 加载默认配置
            try {
                const fs = require('fs');
                const path = require('path');
                const defaultConfigPath = path.join(__dirname, 'config', 'default-settings.json');
                const defaultConfigData = fs.readFileSync(defaultConfigPath, 'utf8');
                appState.settings = JSON.parse(defaultConfigData);
                console.log('📄 使用默认配置');
            } catch (fileError) {
                console.error('🚨 默认配置文件读取失败:', fileError);
                // 使用内置默认配置
                appState.settings = createDefaultSettings();
            }
        }
    } catch (error) {
        console.error('🚨 配置加载失败:', error);
        // 使用内置默认配置
        appState.settings = createDefaultSettings();
    }
}

function createDefaultSettings() {
    const defaultSettings = {
        app: {
            name: "项老师超级总裁助理指挥中心",
            version: "1.0.3-2025-07-29-06-45-00",
            lastStartTime: "",
            autoStart: false
        },
        websocket: {
            serverUrl: CONFIG.WS_SERVER,
            reconnectDelay: 5000,
            heartbeatInterval: 60000,
            connectionTimeout: 30000
        },
        api: {
            baseUrl: CONFIG.API_BASE_URL,
            smartStartUrl: CONFIG.SMART_START_URL,
            timeout: 30000
        },
        devices: {},
        ui: {
            theme: "dark",
            particleCount: 50,
            animationEnabled: true,
            logLevel: "info"
        }
    };

    // 初始化设备配置
    for (let i = 1; i <= 6; i++) {
        const deviceId = i.toString().padStart(3, '0');
        defaultSettings.devices[deviceId] = {
            enabled: true,
            mode: "network",
            interval: 10,
            deviation: 1,
            restRule: { sendCount: 10, restHours: 1 },
            sentCount: 0,
            isRunning: false,
            lastSentTime: null,
            messages: {
                fixed: ["继续"],
                random: ["继续", "下一步", "好的"],
                local: ["继续", "下一步", "开始", "执行"],
                network: {
                    template: "{publisher_name}发给项老师助理{assistant}的{task_id}号{task_type}任务：{content}，{other_requirements}"
                }
            }
        };
    }

    return defaultSettings;
}

async function saveSettings() {
    try {
        const result = await ipcRenderer.invoke('save-config', appState.settings);
        if (result.success) {
            console.log('✅ 配置保存成功');
            showNotification('设置已保存', 'success');
        } else {
            console.error('❌ 配置保存失败:', result.error);
            showNotification('设置保存失败', 'error');
        }
    } catch (error) {
        console.error('🚨 配置保存异常:', error);
        showNotification('设置保存异常', 'error');
    }
}

// 通知系统
function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg text-white font-medium animate__animated animate__fadeInRight ${
        type === 'success' ? 'bg-green-600' :
        type === 'error' ? 'bg-red-600' :
        type === 'warning' ? 'bg-yellow-600' : 'bg-blue-600'
    }`;
    notification.textContent = message;

    document.body.appendChild(notification);

    // 3秒后自动移除
    setTimeout(() => {
        notification.classList.add('animate__fadeOutRight');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 500);
    }, 3000);
}

// UI交互函数
function cycleMode(deviceId) {
    const device = appState.getDevice(deviceId);
    if (!device) return;

    const modes = ['fixed', 'random', 'local', 'network'];
    const currentIndex = modes.indexOf(device.mode);
    const nextIndex = (currentIndex + 1) % modes.length;
    const nextMode = modes[nextIndex];

    appState.updateDevice(deviceId, { mode: nextMode });

    const modeEl = document.getElementById(`device${deviceId}-mode`);
    if (modeEl) {
        const modeNames = {
            'fixed': '固定模式',
            'random': '随机模式',
            'local': '本地模式',
            'network': '网络模式'
        };
        modeEl.textContent = modeNames[nextMode];
    }
}

function cycleInterval(deviceId) {
    const device = appState.getDevice(deviceId);
    if (!device) return;

    const intervals = [10, 15, 20, 30];
    const currentIndex = intervals.indexOf(device.interval);
    const nextIndex = (currentIndex + 1) % intervals.length;
    const nextInterval = intervals[nextIndex];

    appState.updateDevice(deviceId, { interval: nextInterval });

    const intervalEl = document.getElementById(`device${deviceId}-interval`);
    if (intervalEl) {
        intervalEl.textContent = `间隔${nextInterval}分钟`;
    }
}

function cycleDeviation(deviceId) {
    const device = appState.getDevice(deviceId);
    if (!device) return;

    const deviations = [1, 2, 5, 10];
    const currentIndex = deviations.indexOf(device.deviation);
    const nextIndex = (currentIndex + 1) % deviations.length;
    const nextDeviation = deviations[nextIndex];

    appState.updateDevice(deviceId, { deviation: nextDeviation });

    const deviationEl = document.getElementById(`device${deviceId}-deviation`);
    if (deviationEl) {
        deviationEl.textContent = `偏差${nextDeviation}分钟`;
    }
}

function cycleRestRule(deviceId) {
    const device = appState.getDevice(deviceId);
    if (!device) return;

    const rules = [
        { sendCount: 10, restHours: 1 },
        { sendCount: 20, restHours: 2 },
        { sendCount: 30, restHours: 3 },
        { sendCount: 50, restHours: 5 }
    ];

    const currentIndex = rules.findIndex(rule =>
        rule.sendCount === device.restRule.sendCount &&
        rule.restHours === device.restRule.restHours
    );
    const nextIndex = (currentIndex + 1) % rules.length;
    const nextRule = rules[nextIndex];

    appState.updateDevice(deviceId, { restRule: nextRule });

    const restEl = document.getElementById(`device${deviceId}-rest`);
    if (restEl) {
        restEl.textContent = `发${nextRule.sendCount}次休${nextRule.restHours}小时`;
    }
}

function toggleDevice(deviceId) {
    const device = appState.getDevice(deviceId);
    if (!device) return;

    const newRunningState = !device.isRunning;
    appState.updateDevice(deviceId, { isRunning: newRunningState });

    if (newRunningState) {
        // 开始运行，立即发送第一条消息
        messageSender.sendMessage(deviceId);
    } else {
        // 停止运行，清除定时器
        const timerId = appState.timers.get(`send_${deviceId}`);
        if (timerId) {
            clearTimeout(timerId);
            appState.timers.delete(`send_${deviceId}`);
        }
        appState.updateDevice(deviceId, { nextSendTime: null });
    }
}

function stopDevice(deviceId) {
    const device = appState.getDevice(deviceId);
    if (!device) return;

    appState.updateDevice(deviceId, {
        isRunning: false,
        nextSendTime: null,
        sentCount: 0
    });

    // 清除定时器
    const timerId = appState.timers.get(`send_${deviceId}`);
    if (timerId) {
        clearTimeout(timerId);
        appState.timers.delete(`send_${deviceId}`);
    }
}

function sendMessage(deviceId) {
    messageSender.sendMessage(deviceId);
}

// 批量操作函数
function reconnectAll() {
    console.log('🔄 重新连接所有设备...');
    wsManager.disconnectAll();
    setTimeout(() => {
        wsManager.connectAll();
    }, 2000);
    showNotification('正在重新连接所有设备...', 'info');
}

function startAll() {
    console.log('▶️ 启动所有设备...');
    for (let i = 1; i <= 6; i++) {
        const deviceId = i.toString().padStart(3, '0');
        const device = appState.getDevice(deviceId);
        if (device && device.enabled && !device.isRunning) {
            toggleDevice(deviceId);
        }
    }
    showNotification('所有设备已启动', 'success');
}

function stopAll() {
    console.log('⏹️ 停止所有设备...');
    for (let i = 1; i <= 6; i++) {
        const deviceId = i.toString().padStart(3, '0');
        stopDevice(deviceId);
    }
    showNotification('所有设备已停止', 'info');
}

function sendAll() {
    console.log('📤 发送所有设备消息...');
    for (let i = 1; i <= 6; i++) {
        const deviceId = i.toString().padStart(3, '0');
        const device = appState.getDevice(deviceId);
        if (device && device.enabled) {
            messageSender.sendMessage(deviceId);
        }
    }
    showNotification('已发送所有设备消息', 'success');
}

// 窗口控制函数
function minimizeWindow() {
    // 通过IPC发送最小化请求
    ipcRenderer.send('window-minimize');
}

function maximizeWindow() {
    // 通过IPC发送最大化请求
    ipcRenderer.send('window-maximize');
}

function closeWindow() {
    // 通过IPC发送关闭请求
    ipcRenderer.send('window-close');
}

function restoreDefaults() {
    if (confirm('确定要恢复默认设置吗？这将清除所有自定义配置。')) {
        // 重新加载默认配置
        const defaultConfig = require('./config/default-settings.json');
        appState.settings = defaultConfig;

        // 重置所有设备状态
        for (let i = 1; i <= 6; i++) {
            const deviceId = i.toString().padStart(3, '0');
            stopDevice(deviceId);

            appState.updateDevice(deviceId, {
                mode: 'network',
                interval: 10,
                deviation: 1,
                restRule: { sendCount: 10, restHours: 1 },
                sentCount: 0
            });

            // 更新UI
            const modeEl = document.getElementById(`device${deviceId}-mode`);
            const intervalEl = document.getElementById(`device${deviceId}-interval`);
            const deviationEl = document.getElementById(`device${deviceId}-deviation`);
            const restEl = document.getElementById(`device${deviceId}-rest`);

            if (modeEl) modeEl.textContent = '网络模式';
            if (intervalEl) intervalEl.textContent = '间隔10分钟';
            if (deviationEl) deviationEl.textContent = '偏差1分钟';
            if (restEl) restEl.textContent = '发10次休1小时';
        }

        showNotification('已恢复默认设置', 'success');
    }
}

// 模态框和设置管理
function showSettings(deviceId) {
    // 创建设置模态框
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 z-50 flex items-center justify-center modal-backdrop';
    modal.style.zIndex = '1000'; // 确保设置模态框在合适的层级
    modal.innerHTML = `
        <div class="glass rounded-2xl p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-2xl font-bold text-white">助理${deviceId}任务设置</h2>
                <button onclick="closeModal()" class="text-gray-400 hover:text-white text-2xl">&times;</button>
            </div>

            <div class="space-y-6">
                <!-- 模式选择标签页 -->
                <div class="flex space-x-2 mb-4">
                    <button class="tab-btn active" onclick="switchTab('fixed')" data-tab="fixed">固定模式</button>
                    <button class="tab-btn" onclick="switchTab('random')" data-tab="random">随机模式</button>
                    <button class="tab-btn" onclick="switchTab('local')" data-tab="local">本地模式</button>
                    <button class="tab-btn" onclick="switchTab('network')" data-tab="network">网络模式</button>
                </div>

                <!-- 固定模式 -->
                <div id="tab-fixed" class="tab-content">
                    <h3 class="text-lg font-semibold mb-3">固定消息</h3>
                    <textarea id="fixed-message" class="w-full h-20 bg-gray-800 text-white rounded-lg p-3 border border-gray-600" placeholder="输入固定消息...">继续</textarea>
                    <p class="text-sm text-gray-400 mt-2">固定模式将始终发送相同的消息</p>
                </div>

                <!-- 随机模式 -->
                <div id="tab-random" class="tab-content hidden">
                    <h3 class="text-lg font-semibold mb-3">随机消息列表</h3>
                    <div id="random-messages" class="space-y-2 mb-3">
                        <div class="flex items-center space-x-2">
                            <input type="text" class="flex-1 bg-gray-800 text-white rounded-lg p-2 border border-gray-600" value="继续">
                            <button class="px-3 py-2 bg-red-600 hover:bg-red-700 rounded-lg text-sm" onclick="removeMessage(this)">删除</button>
                        </div>
                        <div class="flex items-center space-x-2">
                            <input type="text" class="flex-1 bg-gray-800 text-white rounded-lg p-2 border border-gray-600" value="下一步">
                            <button class="px-3 py-2 bg-red-600 hover:bg-red-700 rounded-lg text-sm" onclick="removeMessage(this)">删除</button>
                        </div>
                    </div>
                    <button class="px-4 py-2 bg-green-600 hover:bg-green-700 rounded-lg text-sm" onclick="addRandomMessage()">添加消息</button>
                </div>

                <!-- 本地模式 -->
                <div id="tab-local" class="tab-content hidden">
                    <h3 class="text-lg font-semibold mb-3">本地顺序消息</h3>
                    <div id="local-messages" class="space-y-2 mb-3">
                        <div class="flex items-center space-x-2">
                            <span class="w-8 text-center text-gray-400">1</span>
                            <input type="text" class="flex-1 bg-gray-800 text-white rounded-lg p-2 border border-gray-600" value="继续">
                            <button class="px-3 py-2 bg-red-600 hover:bg-red-700 rounded-lg text-sm" onclick="removeMessage(this)">删除</button>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="w-8 text-center text-gray-400">2</span>
                            <input type="text" class="flex-1 bg-gray-800 text-white rounded-lg p-2 border border-gray-600" value="下一步">
                            <button class="px-3 py-2 bg-red-600 hover:bg-red-700 rounded-lg text-sm" onclick="removeMessage(this)">删除</button>
                        </div>
                    </div>
                    <button class="px-4 py-2 bg-green-600 hover:bg-green-700 rounded-lg text-sm" onclick="addLocalMessage()">添加消息</button>
                </div>

                <!-- 网络模式 -->
                <div id="tab-network" class="tab-content hidden">
                    <h3 class="text-lg font-semibold mb-3">网络消息模板</h3>
                    <textarea id="network-template" class="w-full h-24 bg-gray-800 text-white rounded-lg p-3 border border-gray-600" placeholder="输入消息模板...">{publisher_name}发给项老师助理{assistant}的{task_id}号{task_type}任务：{content}，{other_requirements}</textarea>
                    <div class="mt-3">
                        <button class="px-3 py-1 bg-blue-600 hover:bg-blue-700 rounded text-sm" onclick="showFieldReference()">字段说明</button>
                        <button class="px-3 py-1 bg-purple-600 hover:bg-purple-700 rounded text-sm" onclick="previewNetworkMessage('${deviceId}')">预览消息</button>
                    </div>
                    <div id="network-preview" class="mt-3 p-3 bg-gray-800 rounded-lg hidden">
                        <h4 class="text-sm font-semibold mb-2">消息预览：</h4>
                        <p class="text-sm text-gray-300" id="preview-text"></p>
                    </div>
                </div>


            </div>

            <div class="flex justify-end space-x-3 mt-6">
                <button onclick="closeModal()" class="px-6 py-2 bg-gray-600 hover:bg-gray-700 rounded-lg">取消</button>
                <button onclick="saveDeviceSettings('${deviceId}')" class="px-6 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg">保存设置</button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // 加载当前设备设置
    loadDeviceSettings(deviceId);
}

function closeModal() {
    const modal = document.querySelector('.modal-backdrop');
    if (modal) {
        document.body.removeChild(modal);
    }
}

function switchTab(tabName) {
    // 隐藏所有标签内容
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.add('hidden');
    });

    // 移除所有标签按钮的active类
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    // 显示选中的标签内容
    const targetContent = document.getElementById(`tab-${tabName}`);
    if (targetContent) {
        targetContent.classList.remove('hidden');
    }

    // 激活选中的标签按钮
    const targetBtn = document.querySelector(`[data-tab="${tabName}"]`);
    if (targetBtn) {
        targetBtn.classList.add('active');
    }
}

function loadDeviceSettings(deviceId) {
    const settings = appState.settings?.devices?.[deviceId];
    if (!settings) return;

    // 加载固定消息
    const fixedTextarea = document.getElementById('fixed-message');
    if (fixedTextarea && settings.messages?.fixed?.[0]) {
        fixedTextarea.value = settings.messages.fixed[0];
    }

    // 加载网络模板
    const networkTextarea = document.getElementById('network-template');
    if (networkTextarea && settings.messages?.network?.template) {
        networkTextarea.value = settings.messages.network.template;
    }

    // 加载随机消息
    const randomContainer = document.getElementById('random-messages');
    if (randomContainer && settings.messages?.random) {
        randomContainer.innerHTML = '';
        settings.messages.random.forEach(message => {
            addRandomMessageWithValue(message);
        });
    }

    // 加载本地消息
    const localContainer = document.getElementById('local-messages');
    if (localContainer && settings.messages?.local) {
        localContainer.innerHTML = '';
        settings.messages.local.forEach((message, index) => {
            addLocalMessageWithValue(message, index + 1);
        });
    }
}

function saveDeviceSettings(deviceId) {
    if (!appState.settings.devices) {
        appState.settings.devices = {};
    }

    if (!appState.settings.devices[deviceId]) {
        appState.settings.devices[deviceId] = { messages: {} };
    }

    // 保存固定消息
    const fixedMessage = document.getElementById('fixed-message')?.value || '继续';
    appState.settings.devices[deviceId].messages.fixed = [fixedMessage];

    // 保存网络模板
    const networkTemplate = document.getElementById('network-template')?.value ||
        '{publisher_name}发给项老师助理{assistant}的{task_id}号{task_type}任务：{content}，{other_requirements}';
    appState.settings.devices[deviceId].messages.network = { template: networkTemplate };

    // 保存随机消息
    const randomInputs = document.querySelectorAll('#random-messages input');
    const randomMessages = Array.from(randomInputs).map(input => input.value).filter(value => value.trim());
    appState.settings.devices[deviceId].messages.random = randomMessages.length > 0 ? randomMessages : ['继续'];

    // 保存本地消息
    const localInputs = document.querySelectorAll('#local-messages input');
    const localMessages = Array.from(localInputs).map(input => input.value).filter(value => value.trim());
    appState.settings.devices[deviceId].messages.local = localMessages.length > 0 ? localMessages : ['继续'];

    // 保存到文件
    saveSettings();

    closeModal();
    showNotification(`设备${deviceId}设置已保存`, 'success');
}

// 消息管理辅助函数
function addRandomMessage() {
    const container = document.getElementById('random-messages');
    if (!container) return;

    const messageDiv = document.createElement('div');
    messageDiv.className = 'flex items-center space-x-2';
    messageDiv.innerHTML = `
        <input type="text" class="flex-1 bg-gray-800 text-white rounded-lg p-2 border border-gray-600" placeholder="输入消息...">
        <button class="px-3 py-2 bg-red-600 hover:bg-red-700 rounded-lg text-sm" onclick="removeMessage(this)">删除</button>
    `;
    container.appendChild(messageDiv);
}

function addRandomMessageWithValue(value) {
    const container = document.getElementById('random-messages');
    if (!container) return;

    const messageDiv = document.createElement('div');
    messageDiv.className = 'flex items-center space-x-2';
    messageDiv.innerHTML = `
        <input type="text" class="flex-1 bg-gray-800 text-white rounded-lg p-2 border border-gray-600" value="${value}">
        <button class="px-3 py-2 bg-red-600 hover:bg-red-700 rounded-lg text-sm" onclick="removeMessage(this)">删除</button>
    `;
    container.appendChild(messageDiv);
}

function addLocalMessage() {
    const container = document.getElementById('local-messages');
    if (!container) return;

    const index = container.children.length + 1;
    const messageDiv = document.createElement('div');
    messageDiv.className = 'flex items-center space-x-2';
    messageDiv.innerHTML = `
        <span class="w-8 text-center text-gray-400">${index}</span>
        <input type="text" class="flex-1 bg-gray-800 text-white rounded-lg p-2 border border-gray-600" placeholder="输入消息...">
        <button class="px-3 py-2 bg-red-600 hover:bg-red-700 rounded-lg text-sm" onclick="removeMessage(this)">删除</button>
    `;
    container.appendChild(messageDiv);
}

function addLocalMessageWithValue(value, index) {
    const container = document.getElementById('local-messages');
    if (!container) return;

    const messageDiv = document.createElement('div');
    messageDiv.className = 'flex items-center space-x-2';
    messageDiv.innerHTML = `
        <span class="w-8 text-center text-gray-400">${index}</span>
        <input type="text" class="flex-1 bg-gray-800 text-white rounded-lg p-2 border border-gray-600" value="${value}">
        <button class="px-3 py-2 bg-red-600 hover:bg-red-700 rounded-lg text-sm" onclick="removeMessage(this)">删除</button>
    `;
    container.appendChild(messageDiv);
}

function removeMessage(button) {
    const messageDiv = button.parentElement;
    const container = messageDiv.parentElement;
    container.removeChild(messageDiv);

    // 如果是本地消息，重新编号
    if (container.id === 'local-messages') {
        const messages = container.children;
        for (let i = 0; i < messages.length; i++) {
            const numberSpan = messages[i].querySelector('span');
            if (numberSpan) {
                numberSpan.textContent = i + 1;
            }
        }
    }
}

function showFieldReference() {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 flex items-center justify-center modal-backdrop';
    modal.style.zIndex = '9999'; // 设置最高层级
    modal.innerHTML = `
        <div class="glass rounded-2xl p-6 max-w-lg w-full mx-4" style="z-index: 10000;">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-xl font-bold text-white">字段说明</h3>
                <button onclick="closeFieldReference()" class="text-gray-400 hover:text-white text-2xl">&times;</button>
            </div>
            <div class="space-y-3 text-sm text-white">
                <div class="flex items-center space-x-3">
                    <code class="bg-gray-800 px-2 py-1 rounded text-yellow-400 cursor-pointer hover:bg-gray-700 hover:text-yellow-300 transition-all duration-200 transform hover:scale-105 active:scale-95" onclick="copyFieldWithAnimation(this, '{publisher_name}')">{publisher_name}</code>
                    <span>- 发布者姓名</span>
                </div>
                <div class="flex items-center space-x-3">
                    <code class="bg-gray-800 px-2 py-1 rounded text-yellow-400 cursor-pointer hover:bg-gray-700 hover:text-yellow-300 transition-all duration-200 transform hover:scale-105 active:scale-95" onclick="copyFieldWithAnimation(this, '{assistant}')">{assistant}</code>
                    <span>- 助理编号</span>
                </div>
                <div class="flex items-center space-x-3">
                    <code class="bg-gray-800 px-2 py-1 rounded text-yellow-400 cursor-pointer hover:bg-gray-700 hover:text-yellow-300 transition-all duration-200 transform hover:scale-105 active:scale-95" onclick="copyFieldWithAnimation(this, '{task_id}')">{task_id}</code>
                    <span>- 任务ID</span>
                </div>
                <div class="flex items-center space-x-3">
                    <code class="bg-gray-800 px-2 py-1 rounded text-yellow-400 cursor-pointer hover:bg-gray-700 hover:text-yellow-300 transition-all duration-200 transform hover:scale-105 active:scale-95" onclick="copyFieldWithAnimation(this, '{task_type}')">{task_type}</code>
                    <span>- 任务类型</span>
                </div>
                <div class="flex items-center space-x-3">
                    <code class="bg-gray-800 px-2 py-1 rounded text-yellow-400 cursor-pointer hover:bg-gray-700 hover:text-yellow-300 transition-all duration-200 transform hover:scale-105 active:scale-95" onclick="copyFieldWithAnimation(this, '{content}')">{content}</code>
                    <span>- 任务内容</span>
                </div>
                <div class="flex items-center space-x-3">
                    <code class="bg-gray-800 px-2 py-1 rounded text-yellow-400 cursor-pointer hover:bg-gray-700 hover:text-yellow-300 transition-all duration-200 transform hover:scale-105 active:scale-95" onclick="copyFieldWithAnimation(this, '{other_requirements}')">{other_requirements}</code>
                    <span>- 其他要求</span>
                </div>
            </div>
            <div class="text-xs text-gray-400 mt-4 text-center">
                💡 点击字段名可复制到剪贴板 | ✨ 点击时会有动画效果
            </div>
            <div class="flex justify-end mt-6">
                <button onclick="closeFieldReference()" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg text-white">确定</button>
            </div>
        </div>
    `;
    document.body.appendChild(modal);
}

function closeFieldReference() {
    const modals = document.querySelectorAll('.modal-backdrop');
    const fieldModal = modals[modals.length - 1]; // 获取最后一个模态框
    if (fieldModal) {
        document.body.removeChild(fieldModal);
    }
}

// 带动画效果的字段复制功能
function copyFieldWithAnimation(element, text) {
    // 添加复制动画效果
    element.classList.add('animate__animated', 'animate__pulse');

    // 临时改变背景色表示点击
    const originalClasses = element.className;
    element.className = element.className.replace('bg-gray-800', 'bg-green-600');
    element.className = element.className.replace('text-yellow-400', 'text-white');

    // 执行复制操作
    if (navigator.clipboard && window.isSecureContext) {
        // 使用现代API
        navigator.clipboard.writeText(text).then(() => {
            showCopySuccessAnimation(element, text);
        }).catch(err => {
            console.error('复制失败:', err);
            fallbackCopyTextToClipboard(text, element);
        });
    } else {
        // 降级方案
        fallbackCopyTextToClipboard(text, element);
    }

    // 500毫秒后恢复原始样式
    setTimeout(() => {
        element.className = originalClasses;
        element.classList.remove('animate__animated', 'animate__pulse');
    }, 500);
}

// 显示复制成功动画
function showCopySuccessAnimation(element, text) {
    // 创建复制成功提示
    const copyTip = document.createElement('div');
    copyTip.className = 'fixed z-50 px-3 py-1 bg-green-600 text-white text-xs rounded-lg animate__animated animate__fadeInUp';
    copyTip.textContent = '已复制!';
    copyTip.style.pointerEvents = 'none';

    // 计算位置（在元素上方显示）
    const rect = element.getBoundingClientRect();
    copyTip.style.left = (rect.left + rect.width / 2 - 30) + 'px';
    copyTip.style.top = (rect.top - 35) + 'px';

    document.body.appendChild(copyTip);

    // 1.5秒后移除提示
    setTimeout(() => {
        copyTip.classList.add('animate__fadeOutUp');
        setTimeout(() => {
            if (document.body.contains(copyTip)) {
                document.body.removeChild(copyTip);
            }
        }, 500);
    }, 1000);

    // 显示通知
    showNotification(`已复制: ${text}`, 'success');
}

// 复制到剪贴板功能（保持兼容性）
function copyToClipboard(text) {
    if (navigator.clipboard && window.isSecureContext) {
        // 使用现代API
        navigator.clipboard.writeText(text).then(() => {
            showNotification(`已复制: ${text}`, 'success');
        }).catch(err => {
            console.error('复制失败:', err);
            fallbackCopyTextToClipboard(text);
        });
    } else {
        // 降级方案
        fallbackCopyTextToClipboard(text);
    }
}

function fallbackCopyTextToClipboard(text, element = null) {
    const textArea = document.createElement("textarea");
    textArea.value = text;

    // 避免滚动到底部
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";
    textArea.style.opacity = "0";

    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        const successful = document.execCommand('copy');
        if (successful) {
            if (element) {
                showCopySuccessAnimation(element, text);
            } else {
                showNotification(`已复制: ${text}`, 'success');
            }
        } else {
            showNotification('复制失败', 'error');
        }
    } catch (err) {
        console.error('复制失败:', err);
        showNotification('复制失败', 'error');
    }

    document.body.removeChild(textArea);
}

async function previewNetworkMessage(deviceId) {
    const template = document.getElementById('network-template')?.value || '';
    const previewDiv = document.getElementById('network-preview');
    const previewText = document.getElementById('preview-text');

    if (!previewDiv || !previewText) return;

    try {
        // 获取示例任务数据
        const response = await fetch(`${CONFIG.API_BASE_URL}?action=get_next_task&assistant=${deviceId}`);
        const result = await response.json();

        if (result.success && result.task) {
            const formattedMessage = messageSender.formatMessage(template, result.task);
            previewText.textContent = formattedMessage;
            previewDiv.classList.remove('hidden');
        } else {
            previewText.textContent = '暂无可预览的任务数据';
            previewDiv.classList.remove('hidden');
        }
    } catch (error) {
        previewText.textContent = '预览失败：' + error.message;
        previewDiv.classList.remove('hidden');
    }
}

// 日志和消息显示
function showRealTimeMessages() {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 z-50 flex items-center justify-center modal-backdrop';
    modal.innerHTML = `
        <div class="glass rounded-2xl p-6 max-w-5xl w-full mx-4 max-h-[85vh] flex flex-col">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-2xl font-bold text-white flex items-center">
                    <i class="fas fa-comments mr-3 text-blue-400"></i>实时消息监控
                </h2>
                <div class="flex items-center space-x-2">
                    <span class="text-sm text-gray-400">消息数: ${logManager.realTimeMessages.length}</span>
                    <button onclick="closeModal()" class="text-gray-400 hover:text-white text-2xl">&times;</button>
                </div>
            </div>
            <div class="flex items-center justify-between mb-3">
                <div class="flex space-x-2 text-xs">
                    <span class="px-2 py-1 bg-blue-600 rounded">📤 发送</span>
                    <span class="px-2 py-1 bg-green-600 rounded">📥 接收</span>
                    <span class="px-2 py-1 bg-cyan-600 rounded">🔗 连接</span>
                    <span class="px-2 py-1 bg-purple-600 rounded">💓 心跳</span>
                    <span class="px-2 py-1 bg-red-600 rounded">❌ 错误</span>
                </div>
                <div class="flex space-x-2">
                    <button onclick="logManager.updateRealTimeDisplay()" class="px-3 py-1 bg-blue-600 hover:bg-blue-700 rounded text-sm">
                        <i class="fas fa-sync-alt mr-1"></i>刷新
                    </button>
                    <button onclick="clearMessages()" class="px-3 py-1 bg-red-600 hover:bg-red-700 rounded text-sm">
                        <i class="fas fa-trash mr-1"></i>清空
                    </button>
                </div>
            </div>
            <div id="realtime-messages" class="flex-1 bg-gray-900 rounded-lg p-4 overflow-y-auto border border-gray-700">
                <div class="text-gray-400 text-center">正在加载消息...</div>
            </div>
            <div class="flex justify-between items-center mt-4">
                <div class="text-xs text-gray-400">
                    💡 消息自动滚动到最新，最多保存${logManager.maxMessages}条消息
                </div>
                <div class="flex space-x-2">
                    <button onclick="exportRealTimeMessages()" class="px-4 py-2 bg-green-600 hover:bg-green-700 rounded-lg text-sm">
                        <i class="fas fa-download mr-1"></i>导出
                    </button>
                    <button onclick="closeModal()" class="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded-lg text-sm">关闭</button>
                </div>
            </div>
        </div>
    `;
    document.body.appendChild(modal);

    // 立即更新显示
    setTimeout(() => {
        logManager.updateRealTimeDisplay();
    }, 100);
}

function showExecutionLog() {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 z-50 flex items-center justify-center modal-backdrop';
    modal.innerHTML = `
        <div class="glass rounded-2xl p-6 max-w-5xl w-full mx-4 max-h-[85vh] flex flex-col">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-2xl font-bold text-white flex items-center">
                    <i class="fas fa-list-alt mr-3 text-green-400"></i>系统执行日志
                </h2>
                <div class="flex items-center space-x-2">
                    <span class="text-sm text-gray-400">日志数: ${logManager.executionLogs.length}</span>
                    <button onclick="closeModal()" class="text-gray-400 hover:text-white text-2xl">&times;</button>
                </div>
            </div>
            <div class="flex items-center justify-between mb-3">
                <div class="flex space-x-2 text-xs">
                    <span class="px-2 py-1 bg-blue-600 rounded">ℹ️ 信息</span>
                    <span class="px-2 py-1 bg-green-600 rounded">✅ 成功</span>
                    <span class="px-2 py-1 bg-yellow-600 rounded">⚠️ 警告</span>
                    <span class="px-2 py-1 bg-red-600 rounded">❌ 错误</span>
                </div>
                <div class="flex space-x-2">
                    <select id="log-level-filter" class="px-2 py-1 bg-gray-700 text-white rounded text-sm" onchange="filterLogs()">
                        <option value="all">全部级别</option>
                        <option value="info">信息</option>
                        <option value="success">成功</option>
                        <option value="warning">警告</option>
                        <option value="error">错误</option>
                    </select>
                    <select id="log-category-filter" class="px-2 py-1 bg-gray-700 text-white rounded text-sm" onchange="filterLogs()">
                        <option value="all">全部分类</option>
                        <option value="system">系统</option>
                        <option value="websocket">WebSocket</option>
                        <option value="api">API</option>
                        <option value="ui">界面</option>
                        <option value="config">配置</option>
                    </select>
                    <button onclick="logManager.updateExecutionLogDisplay()" class="px-3 py-1 bg-blue-600 hover:bg-blue-700 rounded text-sm">
                        <i class="fas fa-sync-alt mr-1"></i>刷新
                    </button>
                    <button onclick="clearExecutionLogs()" class="px-3 py-1 bg-red-600 hover:bg-red-700 rounded text-sm">
                        <i class="fas fa-trash mr-1"></i>清空
                    </button>
                </div>
            </div>
            <div id="execution-log" class="flex-1 bg-gray-900 rounded-lg p-4 overflow-y-auto border border-gray-700">
                <div class="text-gray-400 text-center">正在加载日志...</div>
            </div>
            <div class="flex justify-between items-center mt-4">
                <div class="text-xs text-gray-400">
                    💡 日志按时间倒序排列，最多保存${logManager.maxLogs}条记录
                </div>
                <div class="flex space-x-2">
                    <button onclick="exportLog()" class="px-4 py-2 bg-green-600 hover:bg-green-700 rounded-lg text-sm">
                        <i class="fas fa-download mr-1"></i>导出JSON
                    </button>
                    <button onclick="exportLogText()" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg text-sm">
                        <i class="fas fa-file-alt mr-1"></i>导出TXT
                    </button>
                    <button onclick="closeModal()" class="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded-lg text-sm">关闭</button>
                </div>
            </div>
        </div>
    `;
    document.body.appendChild(modal);

    // 立即更新显示
    setTimeout(() => {
        logManager.updateExecutionLogDisplay();
    }, 100);
}

function clearMessages() {
    logManager.clearRealTimeMessages();
    showNotification('实时消息已清空', 'info');
}

function clearExecutionLogs() {
    logManager.clearExecutionLogs();
    showNotification('执行日志已清空', 'info');
}

function exportRealTimeMessages() {
    const data = {
        exportTime: new Date().toISOString(),
        messageCount: logManager.realTimeMessages.length,
        messages: logManager.realTimeMessages
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `realtime-messages-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    showNotification('实时消息已导出', 'success');
}

function exportLog() {
    logManager.exportLogs();
    showNotification('日志已导出', 'success');
}

function exportLogText() {
    const logText = logManager.executionLogs.map(entry =>
        `[${entry.timestamp}] [${entry.level.toUpperCase()}] [${entry.category.toUpperCase()}] ${entry.message}${entry.details ? '\n  详情: ' + JSON.stringify(entry.details) : ''}`
    ).join('\n');

    const blob = new Blob([logText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `execution-log-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    showNotification('日志文本已导出', 'success');
}

function filterLogs() {
    const levelFilter = document.getElementById('log-level-filter')?.value || 'all';
    const categoryFilter = document.getElementById('log-category-filter')?.value || 'all';

    let filteredLogs = logManager.executionLogs;

    if (levelFilter !== 'all') {
        filteredLogs = filteredLogs.filter(log => log.level === levelFilter);
    }

    if (categoryFilter !== 'all') {
        filteredLogs = filteredLogs.filter(log => log.category === categoryFilter);
    }

    // 更新显示
    const container = document.getElementById('execution-log');
    if (!container) return;

    const html = filteredLogs.map(entry => {
        const levelColors = {
            'info': 'text-blue-400',
            'success': 'text-green-400',
            'warning': 'text-yellow-400',
            'error': 'text-red-400'
        };

        const levelIcons = {
            'info': 'ℹ️',
            'success': '✅',
            'warning': '⚠️',
            'error': '❌'
        };

        const color = levelColors[entry.level] || 'text-gray-400';
        const icon = levelIcons[entry.level] || '📝';

        return `
            <div class="mb-2 text-sm">
                <span class="text-gray-500">[${entry.timestamp}]</span>
                <span class="${color}">${icon} [${entry.category.toUpperCase()}]</span>
                <span class="text-white">${entry.message}</span>
                ${entry.details ? `<div class="ml-4 text-gray-400 text-xs">${JSON.stringify(entry.details, null, 2)}</div>` : ''}
            </div>
        `;
    }).join('');

    container.innerHTML = html || '<div class="text-gray-400 text-center">没有符合条件的日志</div>';
    container.scrollTop = 0;
}

// 添加CSS样式到页面
const style = document.createElement('style');
style.textContent = `
    .tab-btn {
        padding: 8px 16px;
        background: rgba(75, 85, 99, 0.5);
        color: #d1d5db;
        border-radius: 8px;
        transition: all 0.3s ease;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .tab-btn:hover {
        background: rgba(75, 85, 99, 0.8);
        color: white;
    }

    .tab-btn.active {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        border-color: rgba(255, 255, 255, 0.3);
    }

    .add-task-tab-btn {
        padding: 10px 20px;
        background: rgba(75, 85, 99, 0.5);
        color: #d1d5db;
        border-radius: 8px;
        transition: all 0.3s ease;
        border: 1px solid rgba(255, 255, 255, 0.1);
        display: flex;
        align-items: center;
    }

    .add-task-tab-btn:hover {
        background: rgba(75, 85, 99, 0.8);
        color: white;
    }

    .add-task-tab-btn.active {
        background: linear-gradient(135deg, #22c55e, #16a34a);
        color: white;
        border-color: rgba(255, 255, 255, 0.3);
    }

    /* 自定义输入框样式 */
    .custom-input {
        background-color: #212121;
        height: 40px;
        padding: 10px;
        border: 2px solid white;
        border-radius: 5px;
        color: white;
        transition: all 0.1s ease;
    }

    .custom-input:focus {
        color: rgb(0, 255, 255);
        background-color: #212121;
        outline: none;
        border-color: rgb(0, 255, 255);
        box-shadow: -3px -3px 15px rgb(0, 255, 255);
    }

    /* 文本域特殊处理 */
    .custom-input[class*="h-"] {
        height: auto;
        min-height: 40px;
    }

    /* 选择框特殊处理 */
    .custom-input option {
        background-color: #212121;
        color: white;
    }
`;
document.head.appendChild(style);

// 打开外部链接
function openExternalLink(url) {
    try {
        // 使用Electron的shell模块打开外部链接
        const { shell } = require('electron');
        shell.openExternal(url);
        logManager.addExecutionLog('info', 'system', `打开外部链接: ${url}`);
    } catch (error) {
        console.error('打开外部链接失败:', error);
        logManager.addExecutionLog('error', 'system', '打开外部链接失败', error);

        // 降级方案：使用window.open
        try {
            window.open(url, '_blank');
        } catch (fallbackError) {
            console.error('降级方案也失败:', fallbackError);
            showNotification('无法打开链接', 'error');
        }
    }
}

// 新增任务模态框
function showAddTaskModal(deviceId) {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 z-50 flex items-center justify-center modal-backdrop';
    modal.style.zIndex = '1000';
    modal.innerHTML = `
        <div class="glass rounded-2xl p-6 max-w-3xl w-full mx-4 max-h-[85vh] overflow-y-auto">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-2xl font-bold text-white flex items-center">
                    <i class="fas fa-plus-circle mr-3 text-green-400"></i>新增任务到助理${deviceId}
                </h2>
                <button onclick="closeModal()" class="text-gray-400 hover:text-white text-2xl">&times;</button>
            </div>

            <!-- 选项卡导航 -->
            <div class="flex space-x-2 mb-6">
                <button class="add-task-tab-btn active" onclick="switchAddTaskTab('single')" data-tab="single">
                    <i class="fas fa-file-plus mr-2"></i>新增一篇
                </button>
                <button class="add-task-tab-btn" onclick="switchAddTaskTab('batch')" data-tab="batch">
                    <i class="fas fa-layer-group mr-2"></i>批量新增
                </button>
            </div>

            <!-- 新增一篇 -->
            <div id="add-task-single" class="add-task-tab-content">
                <div class="space-y-4">
                    <div>
                        <select id="single-task-type-${deviceId}" class="custom-input w-full">
                            <option value="">选择任务类型</option>
                            <option value="调研报告">调研报告</option>
                            <option value="网页开发">网页开发</option>
                            <option value="数据分析">数据分析</option>
                            <option value="课程制作">课程制作</option>
                            <option value="个人宣传页">个人宣传页</option>
                            <option value="文生图">文生图</option>
                            <option value="文字生语音">文字生语音</option>
                            <option value="其他">其他</option>
                        </select>
                    </div>
                    <div>
                        <textarea id="single-task-content-${deviceId}" class="custom-input w-full h-24 resize-none" placeholder="输入任务具体内容..."></textarea>
                    </div>
                    <div>
                        <textarea id="single-task-requirements-${deviceId}" class="custom-input w-full h-20 resize-none" placeholder="其他特殊要求（可选）..."></textarea>
                    </div>
                    <div class="flex items-center space-x-4">
                        <select id="single-task-priority-${deviceId}" class="custom-input">
                            <option value="1">最低优先级</option>
                            <option value="3">较低优先级</option>
                            <option value="10">最高优先级</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- 批量新增 -->
            <div id="add-task-batch" class="add-task-tab-content hidden">
                <div class="space-y-4">
                    <div>
                        <select id="batch-task-type-${deviceId}" class="custom-input w-full">
                            <option value="">选择统一任务类型</option>
                            <option value="调研报告">调研报告</option>
                            <option value="网页开发">网页开发</option>
                            <option value="数据分析">数据分析</option>
                            <option value="课程制作">课程制作</option>
                            <option value="个人宣传页">个人宣传页</option>
                            <option value="文生图">文生图</option>
                            <option value="文字生语音">文字生语音</option>
                            <option value="其他">其他</option>
                        </select>
                    </div>
                    <div>
                        <textarea id="batch-task-content-${deviceId}" class="custom-input w-full h-32 resize-none" placeholder="输入多个任务内容，一行一个任务：&#10;任务1：创建企业官网&#10;任务2：设计产品页面&#10;任务3：优化用户体验"></textarea>
                    </div>
                    <div>
                        <textarea id="batch-task-requirements-${deviceId}" class="custom-input w-full h-20 resize-none" placeholder="统一的其他要求（可选，应用到所有任务）..."></textarea>
                    </div>
                    <div class="flex items-center space-x-4">
                        <select id="batch-task-priority-${deviceId}" class="custom-input">
                            <option value="1">最低优先级</option>
                            <option value="3">较低优先级</option>
                            <option value="10">最高优先级</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- 任务提交结果 -->
            <div id="task-submit-result-${deviceId}" class="mt-6 hidden">
                <h4 class="text-md font-medium mb-3 text-green-400">提交结果</h4>
                <div id="task-result-content-${deviceId}" class="bg-gray-900 rounded-lg p-3 text-sm max-h-40 overflow-y-auto"></div>
            </div>

            <!-- 底部按钮 -->
            <div class="flex justify-center items-center space-x-4 mt-6">
                <button onclick="closeModal()" class="px-6 py-2 bg-gray-600 hover:bg-gray-700 rounded-lg">取消</button>
                <button onclick="submitCurrentTask('${deviceId}')" class="px-6 py-2 bg-green-600 hover:bg-green-700 rounded-lg">
                    <i class="fas fa-paper-plane mr-2"></i>提交任务
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
}

// 切换新增任务选项卡
function switchAddTaskTab(tabName) {
    // 隐藏所有选项卡内容
    document.querySelectorAll('.add-task-tab-content').forEach(content => {
        content.classList.add('hidden');
    });

    // 移除所有选项卡按钮的active类
    document.querySelectorAll('.add-task-tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    // 显示选中的选项卡内容
    const targetContent = document.getElementById(`add-task-${tabName}`);
    if (targetContent) {
        targetContent.classList.remove('hidden');
    }

    // 激活选中的选项卡按钮
    const targetBtn = document.querySelector(`[data-tab="${tabName}"]`);
    if (targetBtn) {
        targetBtn.classList.add('active');
    }
}

// 提交当前选中的任务
function submitCurrentTask(deviceId) {
    const singleTab = document.getElementById('add-task-single');
    const batchTab = document.getElementById('add-task-batch');

    if (!singleTab.classList.contains('hidden')) {
        // 提交单个任务
        submitSingleTask(deviceId);
    } else if (!batchTab.classList.contains('hidden')) {
        // 提交批量任务
        submitBatchTasks(deviceId);
    }
}

// 新增任务功能
async function submitSingleTask(deviceId) {
    const taskType = document.getElementById(`single-task-type-${deviceId}`)?.value;
    const content = document.getElementById(`single-task-content-${deviceId}`)?.value;
    const requirements = document.getElementById(`single-task-requirements-${deviceId}`)?.value;
    const priority = document.getElementById(`single-task-priority-${deviceId}`)?.value || '3';

    if (!taskType || !content) {
        showNotification('请填写任务类型和内容', 'error');
        return;
    }

    try {
        const formData = new FormData();
        formData.append('action', 'add_task');
        formData.append('assistant', deviceId);
        formData.append('task_type', taskType);
        formData.append('content', content);
        formData.append('priority', priority);
        if (requirements) {
            formData.append('other_requirements', requirements);
        }

        const response = await fetch(CONFIG.API_BASE_URL, {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (result.success) {
            showTaskSubmitResult(deviceId, [{
                success: true,
                task_id: result.task_id,
                content: content,
                message: result.message
            }]);

            // 清空表单
            document.getElementById(`single-task-type-${deviceId}`).value = '';
            document.getElementById(`single-task-content-${deviceId}`).value = '';
            document.getElementById(`single-task-requirements-${deviceId}`).value = '';

            showNotification('任务添加成功', 'success');
            logManager.addExecutionLog('success', 'api', `设备 ${deviceId} 单个任务添加成功`, result);
        } else {
            showNotification(`任务添加失败: ${result.message || result.error}`, 'error');
            logManager.addExecutionLog('error', 'api', `设备 ${deviceId} 任务添加失败`, result);
        }
    } catch (error) {
        console.error('提交任务失败:', error);
        showNotification('网络错误，请重试', 'error');
        logManager.addExecutionLog('error', 'api', `设备 ${deviceId} 任务提交网络错误`, error);
    }
}

async function submitBatchTasks(deviceId) {
    const taskType = document.getElementById(`batch-task-type-${deviceId}`)?.value;
    const contentText = document.getElementById(`batch-task-content-${deviceId}`)?.value;
    const requirements = document.getElementById(`batch-task-requirements-${deviceId}`)?.value;
    const priority = document.getElementById(`batch-task-priority-${deviceId}`)?.value || '3';

    if (!taskType || !contentText) {
        showNotification('请填写任务类型和内容', 'error');
        return;
    }

    // 按行分割任务内容
    const tasks = contentText.split('\n')
        .map(line => line.trim())
        .filter(line => line.length > 0);

    if (tasks.length === 0) {
        showNotification('请输入至少一个任务', 'error');
        return;
    }

    const results = [];
    let successCount = 0;
    let failCount = 0;

    showNotification(`开始批量添加 ${tasks.length} 个任务...`, 'info');

    for (let i = 0; i < tasks.length; i++) {
        const content = tasks[i];

        try {
            const formData = new FormData();
            formData.append('action', 'add_task');
            formData.append('assistant', deviceId);
            formData.append('task_type', taskType);
            formData.append('content', content);
            formData.append('priority', priority);
            if (requirements) {
                formData.append('other_requirements', requirements);
            }

            const response = await fetch(CONFIG.API_BASE_URL, {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                results.push({
                    success: true,
                    task_id: result.task_id,
                    content: content,
                    message: result.message
                });
                successCount++;
            } else {
                results.push({
                    success: false,
                    content: content,
                    error: result.message || result.error
                });
                failCount++;
            }
        } catch (error) {
            results.push({
                success: false,
                content: content,
                error: error.message
            });
            failCount++;
        }

        // 添加小延迟避免服务器压力
        if (i < tasks.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 200));
        }
    }

    // 显示结果
    showTaskSubmitResult(deviceId, results);

    if (successCount > 0) {
        // 清空表单
        document.getElementById(`batch-task-content-${deviceId}`).value = '';
        document.getElementById(`batch-task-requirements-${deviceId}`).value = '';
    }

    showNotification(`批量添加完成: 成功 ${successCount} 个，失败 ${failCount} 个`,
        failCount === 0 ? 'success' : 'warning');

    logManager.addExecutionLog('info', 'api', `设备 ${deviceId} 批量任务添加完成`, {
        total: tasks.length,
        success: successCount,
        failed: failCount
    });
}

function showTaskSubmitResult(deviceId, results) {
    const resultContainer = document.getElementById(`task-submit-result-${deviceId}`);
    const resultContent = document.getElementById(`task-result-content-${deviceId}`);

    if (!resultContainer || !resultContent) return;

    let html = '';
    results.forEach((result, index) => {
        if (result.success) {
            html += `
                <div class="mb-2 p-2 bg-green-900 border border-green-600 rounded">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle text-green-400 mr-2"></i>
                        <span class="text-green-300">任务ID: ${result.task_id}</span>
                    </div>
                    <div class="text-sm text-gray-300 mt-1">${result.content}</div>
                </div>
            `;
        } else {
            html += `
                <div class="mb-2 p-2 bg-red-900 border border-red-600 rounded">
                    <div class="flex items-center">
                        <i class="fas fa-times-circle text-red-400 mr-2"></i>
                        <span class="text-red-300">添加失败</span>
                    </div>
                    <div class="text-sm text-gray-300 mt-1">${result.content}</div>
                    <div class="text-xs text-red-400 mt-1">错误: ${result.error}</div>
                </div>
            `;
        }
    });

    resultContent.innerHTML = html;
    resultContainer.classList.remove('hidden');

    // 滚动到结果区域
    resultContainer.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', initializeApp);
